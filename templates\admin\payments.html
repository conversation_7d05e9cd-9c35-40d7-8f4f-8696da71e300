{% extends "admin/layout.html" %}

{% block title %}支付记录{% endblock %}

{% block extra_css %}
<style>
    .filters {
        background: white;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .filter-row {
        display: flex;
        gap: 15px;
        align-items: center;
        flex-wrap: wrap;
    }

    .filter-group {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .filter-group label {
        font-weight: 500;
        color: #333;
    }

    .filter-group select,
    .filter-group input {
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
    }

    table {
        width: 100%;
        border-collapse: collapse;
    }

    th,
    td {
        padding: 12px;
        text-align: left;
        border-bottom: 1px solid #eee;
    }

    th {
        background: #f8f9fa;
        font-weight: 600;
        color: #333;
    }

    tr:hover {
        background: #f8f9fa;
    }

    .status-badge {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
    }

    .openid-info {
        font-family: monospace;
        font-size: 12px;
        color: #666;
        cursor: help;
        max-width: 100px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    .status-success {
        background: #d4edda;
        color: #155724;
    }

    .status-paid {
        background: #d4edda;
        color: #155724;
    }

    .status-pending {
        background: #fff3cd;
        color: #856404;
    }

    .status-failed {
        background: #f8d7da;
        color: #721c24;
    }

    .status-refunded {
        background: #e2e3e5;
        color: #383d41;
    }

    .status-refunded {
        background: #e2e3e5;
        color: #383d41;
    }

    /* 模态框样式 */
    .modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
    }

    .modal-content {
        background-color: #fefefe;
        margin: 5% auto;
        padding: 0;
        border: none;
        border-radius: 8px;
        width: 80%;
        max-width: 800px;
        max-height: 90vh;
        overflow-y: auto;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    }

    .modal-header {
        padding: 20px 30px;
        background: #226e7e;
        color: white;
        border-radius: 8px 8px 0 0;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .modal-header h2 {
        margin: 0;
        font-size: 20px;
    }

    .close {
        color: white;
        font-size: 28px;
        font-weight: bold;
        cursor: pointer;
        line-height: 1;
    }

    .close:hover {
        opacity: 0.7;
    }

    .modal-body {
        padding: 30px;
    }

    .detail-section {
        margin-bottom: 30px;
        border-bottom: 1px solid #eee;
        padding-bottom: 20px;
    }

    .detail-section:last-child {
        border-bottom: none;
        margin-bottom: 0;
    }

    .detail-section h3 {
        margin: 0 0 15px 0;
        color: #226e7e;
        font-size: 16px;
        font-weight: 600;
    }

    .detail-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 15px;
    }

    .detail-item {
        display: flex;
        flex-direction: column;
    }

    .detail-item.full-width {
        grid-column: 1 / -1;
    }

    .detail-item label {
        font-weight: 600;
        color: #555;
        margin-bottom: 5px;
        font-size: 14px;
    }

    .detail-item span {
        color: #333;
        font-size: 14px;
    }

    .detail-item .amount {
        font-weight: 600;
        color: #e74c3c;
        font-size: 16px;
    }

    .photo-preview {
        margin-top: 10px;
    }

    .photo-preview img {
        max-width: 200px;
        max-height: 200px;
        border-radius: 8px;
        border: 1px solid #ddd;
        object-fit: cover;
    }

    .loading {
        text-align: center;
        padding: 40px;
        color: #666;
    }

    .error {
        text-align: center;
        padding: 40px;
        color: #e74c3c;
    }

    .valid {
        color: #27ae60;
        font-weight: 600;
    }

    .expired {
        color: #e74c3c;
        font-weight: 600;
    }

    @media (max-width: 768px) {
        .modal-content {
            width: 95%;
            margin: 10% auto;
        }

        .detail-grid {
            grid-template-columns: 1fr;
        }

        .modal-body {
            padding: 20px;
        }
    }

    .pagination {
        padding: 20px;
        display: flex;
        justify-content: center;
        gap: 10px;
    }

    .page-btn {
        padding: 8px 12px;
        border: 1px solid #ddd;
        background: white;
        cursor: pointer;
        border-radius: 4px;
    }

    .page-btn.active {
        background: #667eea;
        color: white;
        border-color: #667eea;
    }

    .stats-cards {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 20px;
        margin-bottom: 20px;
    }

    .stat-card {
        background: white;
        padding: 20px;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        text-align: center;
    }

    .stat-value {
        font-size: 24px;
        font-weight: 600;
        color: #333;
        margin-bottom: 5px;
    }

    .stat-label {
        color: #666;
        font-size: 14px;
    }
</style>
{% endblock %}

{% block content %}
<div class="page-header">
    <h1 class="page-title">支付记录</h1>
    <div class="breadcrumb">
        <a href="/admin/">首页</a> / 支付记录
    </div>
</div>

<div class="stats-cards" id="statsCards">
    <div class="stat-card">
        <div class="stat-value" id="totalAmount">¥0.00</div>
        <div class="stat-label">总收入</div>
    </div>
    <div class="stat-card">
        <div class="stat-value" id="successCount">0</div>
        <div class="stat-label">成功支付</div>
    </div>
    <div class="stat-card">
        <div class="stat-value" id="pendingCount">0</div>
        <div class="stat-label">待支付</div>
    </div>
    <div class="stat-card">
        <div class="stat-value" id="todayAmount">¥0.00</div>
        <div class="stat-label">今日收入</div>
    </div>
</div>

<div class="filters">
    <div class="filter-row">
        <div class="filter-group">
            <label>支付状态：</label>
            <select id="statusFilter">
                <option value="">全部状态</option>
                <option value="PAID">已支付</option>
                <option value="PENDING">待支付</option>
                <option value="FAILED">支付失败</option>
                <option value="REFUNDED">已退款</option>
            </select>
        </div>
        <div class="filter-group">
            <label>日期范围：</label>
            <input type="date" id="startDate">
            <span>至</span>
            <input type="date" id="endDate">
        </div>
        <div class="filter-group">
            <label>搜索：</label>
            <input type="text" id="searchInput" placeholder="学生姓名或订单号">
        </div>
        <button class="btn btn-primary" onclick="loadPayments()">查询</button>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <div class="card-title">支付记录列表</div>
        <div id="tableStats">加载中...</div>
    </div>

    <div id="loadingDiv" class="loading">
        正在加载支付记录...
    </div>

    <div id="errorDiv" class="error" style="display: none;"></div>

    <div id="tableContainer" style="display: none;">
        <table>
            <thead>
                <tr>
                    <th>订单号</th>
                    <th>学生ID</th>
                    <th>学生姓名</th>
                    <th>年级班级</th>
                    <th>支付金额</th>
                    <th>支付方式</th>
                    <th>支付状态</th>
                    <th>微信用户</th>
                    <th>支付时间</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody id="paymentsTableBody">
            </tbody>
        </table>

        <div class="pagination" id="paginationDiv">
        </div>
    </div>
</div>

<!-- 支付详情模态框 -->
<div id="paymentDetailModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h2>支付详情</h2>
            <span class="close" onclick="hidePaymentDetailModal()">&times;</span>
        </div>
        <div class="modal-body">
            <div id="paymentDetailContent">
                <!-- 详情内容将通过JavaScript动态加载 -->
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
    let currentPage = 1;
    let totalPages = 1;

    // 页面加载时获取数据
    document.addEventListener('DOMContentLoaded', function () {
        loadPaymentStats();
        loadPayments();
        setDefaultDates();
    });

    function setDefaultDates() {
        const today = new Date();
        const oneMonthAgo = new Date(today.getFullYear(), today.getMonth() - 1, today.getDate());

        document.getElementById('endDate').value = today.toISOString().split('T')[0];
        document.getElementById('startDate').value = oneMonthAgo.toISOString().split('T')[0];
    }

    async function loadPaymentStats() {
        try {
            const result = await apiCall('/admin/api/payment-stats');

            if (result && result.success) {
                const stats = result.data;
                document.getElementById('totalAmount').textContent = `¥${stats.total_amount || '0.00'}`;
                document.getElementById('successCount').textContent = stats.success_count || '0';
                document.getElementById('pendingCount').textContent = stats.pending_count || '0';
                document.getElementById('todayAmount').textContent = `¥${stats.today_amount || '0.00'}`;
            }
        } catch (error) {
            console.error('加载统计数据失败:', error);
        }
    }

    async function loadPayments(page = 1) {
        const status = document.getElementById('statusFilter').value;
        const startDate = document.getElementById('startDate').value;
        const endDate = document.getElementById('endDate').value;
        const search = document.getElementById('searchInput').value.trim();

        const params = new URLSearchParams({
            page: page,
            per_page: 20
        });

        if (status) params.append('status', status);
        if (startDate) params.append('start_date', startDate);
        if (endDate) params.append('end_date', endDate);
        if (search) params.append('search', search);

        try {
            showLoading('tableContainer');

            console.log('正在调用支付API:', `/admin/api/payments?${params}`);
            const result = await apiCall(`/admin/api/payments?${params}`);
            console.log('支付API响应:', result);

            if (result && result.success) {
                displayPayments(result.data);
            } else {
                console.error('支付API调用失败:', result);
                showError('errorDiv', result?.error || '加载失败');
            }
        } catch (error) {
            console.error('支付网络错误:', error);
            showError('errorDiv', '网络错误：' + error.message);
        }
    }

    function displayPayments(data) {
        const { payments, pagination } = data;

        // 更新统计信息
        document.getElementById('tableStats').textContent =
            `共 ${pagination.total} 条支付记录，第 ${pagination.page}/${pagination.pages} 页`;

        // 更新表格
        const tbody = document.getElementById('paymentsTableBody');
        tbody.innerHTML = '';

        payments.forEach(payment => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${payment.order_number || '-'}</td>
                <td>${payment.student_id || '-'}</td>
                <td>${payment.student_name || '-'}</td>
                <td>${(payment.student_grade || '') + ' ' + (payment.student_class || '')}</td>
                <td>¥${payment.amount || '0.00'}</td>
                <td>${getPaymentMethod(payment.payment_method)}</td>
                <td>
                    <span class="status-badge status-${payment.status}">
                        ${getPaymentStatus(payment.status)}
                    </span>
                </td>
                <td>
                    <span class="openid-info" title="${payment.wechat_openid || '未知'}">
                        ${payment.wechat_openid ? (payment.wechat_openid.substring(0, 8) + '...') : '未获取'}
                    </span>
                </td>
                <td>${payment.paid_at ? formatDate(payment.paid_at) : '未支付'}</td>
                <td>
                    <button class="btn btn-primary" onclick="viewPaymentDetail('${payment.id}')">查看详情</button>
                    ${payment.status === 'paid' ? `
                    <button class="btn btn-warning" onclick="markAsRefunded('${payment.id}')">标记退款</button>
                    ` : ''}
                </td>
            `;
            tbody.appendChild(row);
        });

        // 更新分页
        updatePagination(pagination);

        // 显示表格
        document.getElementById('loadingDiv').style.display = 'none';
        document.getElementById('errorDiv').style.display = 'none';
        document.getElementById('tableContainer').style.display = 'block';
    }

    function getPaymentMethod(method) {
        const methodMap = {
            'wechat': '微信支付',
            'alipay': '支付宝',
            'bank': '银行转账'
        };
        return methodMap[method] || method || '-';
    }

    function getPaymentStatus(status) {
        const statusMap = {
            'success': '支付成功',
            'paid': '已支付',
            'pending': '待支付',
            'failed': '支付失败',
            'refunded': '已退款'
        };
        return statusMap[status] || status;
    }

    function updatePagination(pagination) {
        const paginationDiv = document.getElementById('paginationDiv');
        paginationDiv.innerHTML = '';

        currentPage = pagination.page;
        totalPages = pagination.pages;

        // 上一页
        if (pagination.has_prev) {
            const prevBtn = document.createElement('button');
            prevBtn.className = 'page-btn';
            prevBtn.textContent = '上一页';
            prevBtn.onclick = () => loadPayments(pagination.page - 1);
            paginationDiv.appendChild(prevBtn);
        }

        // 页码
        for (let i = Math.max(1, pagination.page - 2); i <= Math.min(pagination.pages, pagination.page + 2); i++) {
            const pageBtn = document.createElement('button');
            pageBtn.className = `page-btn ${i === pagination.page ? 'active' : ''}`;
            pageBtn.textContent = i;
            pageBtn.onclick = () => loadPayments(i);
            paginationDiv.appendChild(pageBtn);
        }

        // 下一页
        if (pagination.has_next) {
            const nextBtn = document.createElement('button');
            nextBtn.className = 'page-btn';
            nextBtn.textContent = '下一页';
            nextBtn.onclick = () => loadPayments(pagination.page + 1);
            paginationDiv.appendChild(nextBtn);
        }
    }

    function viewPaymentDetail(paymentId) {
        // 显示加载状态
        showPaymentDetailModal();
        document.getElementById('paymentDetailContent').innerHTML = '<div class="loading">加载中...</div>';

        // 获取支付详情
        fetch(`/admin/api/payment/${paymentId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    renderPaymentDetail(data.data);
                } else {
                    document.getElementById('paymentDetailContent').innerHTML =
                        `<div class="error">加载失败: ${data.error}</div>`;
                }
            })
            .catch(error => {
                console.error('获取支付详情失败:', error);
                document.getElementById('paymentDetailContent').innerHTML =
                    '<div class="error">网络错误，请稍后重试</div>';
            });
    }

    function showPaymentDetailModal() {
        document.getElementById('paymentDetailModal').style.display = 'block';
    }

    function hidePaymentDetailModal() {
        document.getElementById('paymentDetailModal').style.display = 'none';
    }

    function renderPaymentDetail(detail) {
        const content = `
            <div class="detail-section">
                <h3>💳 支付信息</h3>
                <div class="detail-grid">
                    <div class="detail-item">
                        <label>订单号:</label>
                        <span>${detail.payment.order_number}</span>
                    </div>
                    <div class="detail-item">
                        <label>支付金额:</label>
                        <span class="amount">¥${detail.payment.amount}</span>
                    </div>
                    <div class="detail-item">
                        <label>支付方式:</label>
                        <span>${getPaymentMethod(detail.payment.payment_method)}</span>
                    </div>
                    <div class="detail-item">
                        <label>支付状态:</label>
                        <span class="status-badge status-${detail.payment.status}">
                            ${getPaymentStatus(detail.payment.status)}
                        </span>
                    </div>
                    <div class="detail-item">
                        <label>创建时间:</label>
                        <span>${formatDate(detail.payment.created_at)}</span>
                    </div>
                    <div class="detail-item">
                        <label>支付时间:</label>
                        <span>${detail.payment.paid_at ? formatDate(detail.payment.paid_at) : '未支付'}</span>
                    </div>
                    ${detail.payment.status === 'refunded' ? `
                    <div class="detail-item">
                        <label>退款时间:</label>
                        <span>${detail.payment.refunded_at ? formatDate(detail.payment.refunded_at) : '未知'}</span>
                    </div>
                    <div class="detail-item">
                        <label>退款金额:</label>
                        <span>¥${detail.payment.refund_amount || '0.00'}</span>
                    </div>
                    ${detail.payment.refund_id ? `
                    <div class="detail-item">
                        <label>退款单号:</label>
                        <span>${detail.payment.refund_id}</span>
                    </div>
                    ` : ''}
                    ${detail.payment.refund_reason ? `
                    <div class="detail-item">
                        <label>退款原因:</label>
                        <span>${detail.payment.refund_reason}</span>
                    </div>
                    ` : ''}
                    ` : ''}
                    ${detail.payment.transaction_id ? `
                    <div class="detail-item">
                        <label>微信交易号:</label>
                        <span>${detail.payment.transaction_id}</span>
                    </div>
                    ` : ''}
                </div>
            </div>

            <div class="detail-section">
                <h3>👤 学生信息</h3>
                <div class="detail-grid">
                    <div class="detail-item">
                        <label>姓名:</label>
                        <span>${detail.student.name}</span>
                    </div>
                    <div class="detail-item">
                        <label>学生电话:</label>
                        <span>${detail.student.phone || '未填写'}</span>
                    </div>
                    <div class="detail-item">
                        <label>年级班级:</label>
                        <span>${detail.student.grade_level} ${detail.student.class_name}</span>
                    </div>
                    <div class="detail-item">
                        <label>入学年份:</label>
                        <span>${detail.student.enrollment_year}</span>
                    </div>
                    <div class="detail-item">
                        <label>家长电话:</label>
                        <span>${detail.student.parent_phone || '未填写'}</span>
                    </div>
                </div>
            </div>

            <div class="detail-section">
                <h3>🎫 学生卡信息</h3>
                <div class="detail-grid">
                    <div class="detail-item">
                        <label>卡号:</label>
                        <span>${detail.student_card.card_number}</span>
                    </div>
                    <div class="detail-item">
                        <label>卡片状态:</label>
                        <span class="status-badge status-${detail.student_card.status}">
                            ${getCardStatus(detail.student_card.status)}
                        </span>
                    </div>
                    <div class="detail-item">
                        <label>发卡日期:</label>
                        <span>${detail.student_card.issue_date ? formatDate(detail.student_card.issue_date) : '未发卡'}</span>
                    </div>
                    <div class="detail-item">
                        <label>过期日期:</label>
                        <span>${detail.student_card.expire_date ? formatDate(detail.student_card.expire_date) : '未设置'}</span>
                    </div>
                    <div class="detail-item">
                        <label>是否过期:</label>
                        <span class="${detail.student_card.is_expired ? 'expired' : 'valid'}">
                            ${detail.student_card.is_expired ? '已过期' : '有效'}
                        </span>
                    </div>
                </div>
            </div>

            ${detail.photo ? `
            <div class="detail-section">
                <h3>📷 照片信息</h3>
                <div class="detail-grid">
                    <div class="detail-item">
                        <label>原始文件名:</label>
                        <span>${detail.photo.original_filename || '未知'}</span>
                    </div>
                    <div class="detail-item">
                        <label>文件大小:</label>
                        <span>${detail.photo.file_size ? (detail.photo.file_size / 1024).toFixed(1) + ' KB' : '未知'}</span>
                    </div>
                    <div class="detail-item">
                        <label>图片尺寸:</label>
                        <span>${detail.photo.width && detail.photo.height ? detail.photo.width + ' × ' + detail.photo.height : '未知'}</span>
                    </div>
                    <div class="detail-item">
                        <label>审核状态:</label>
                        <span class="${detail.photo.is_approved ? 'valid' : 'expired'}">
                            ${detail.photo.is_approved ? '已审核' : '待审核'}
                        </span>
                    </div>
                    <div class="detail-item">
                        <label>上传时间:</label>
                        <span>${formatDate(detail.photo.created_at)}</span>
                    </div>
                    <div class="detail-item full-width">
                        <label>照片预览:</label>
                        <div class="photo-preview">
                            <img src="${detail.photo.file_path}" alt="学生照片"
                                 onerror="this.src='/static/images/default-avatar.png'">
                        </div>
                    </div>
                </div>
            </div>
            ` : ''}
        `;

        document.getElementById('paymentDetailContent').innerHTML = content;
    }

    function getCardStatus(status) {
        const statusMap = {
            'PENDING': '待处理',
            'ACTIVE': '已激活',
            'EXPIRED': '已过期',
            'CANCELLED': '已取消'
        };
        return statusMap[status] || status;
    }

    // 点击模态框外部关闭
    window.onclick = function (event) {
        const modal = document.getElementById('paymentDetailModal');
        if (event.target === modal) {
            hidePaymentDetailModal();
        }
    }



    // 回车搜索
    document.getElementById('searchInput').addEventListener('keypress', function (e) {
        if (e.key === 'Enter') {
            loadPayments();
        }
    });

    // 手动标记为退款
    function markAsRefunded(paymentId) {
        if (!confirm('确认要将此订单标记为已退款吗？\n\n请确保您已在微信商户平台完成退款操作。')) {
            return;
        }

        const refundAmount = prompt('请输入退款金额（元）：');
        if (!refundAmount || isNaN(refundAmount) || parseFloat(refundAmount) <= 0) {
            alert('请输入有效的退款金额');
            return;
        }

        const refundReason = prompt('请输入退款原因（可选）：') || '手动标记退款';

        fetch('/admin/api/mark-refunded', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                payment_id: paymentId,
                refund_amount: parseFloat(refundAmount),
                refund_reason: refundReason
            })
        })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('退款状态更新成功！');
                    loadPayments(); // 重新加载支付列表
                } else {
                    alert('更新失败：' + (data.error || '未知错误'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('网络错误，请重试');
            });
    }
</script>
{% endblock %}