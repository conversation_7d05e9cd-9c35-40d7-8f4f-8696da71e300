# 琼中附属中学学生卡管理系统

## 📋 项目概述

基于Security_Middleware项目开发的完整学生卡管理系统，集成学生信息管理、照片采集、微信支付、实体卡制作等全流程功能。支持移动端家长操作和后台管理员操作。

## 🎯 主要功能

### 后台管理系统
- ✅ 学生信息管理（支持所有年级：初一到初三、高一到高三）
- ✅ 班级格式转换和年级筛选
- ✅ 学生卡二维码生成（包含查询URL和时效验证）
- ✅ 学生卡预览和批量生成
- ✅ 制卡数据导出（包含有效期信息）
- ✅ 支付记录管理和订单跟踪
- ✅ 照片审核和管理
- ✅ 重新上传照片管理（查看和管理学生重新提交的照片）
- ✅ 学生卡状态管理（待制作、已支付、制作中、已完成、已取消）
- ✅ 材料生成模式选择（全部、仅未生成、重新生成）
- ✅ 年级自动修正功能（根据入学年份智能判断）
- ✅ 人脸检测和智能裁剪（自动方向检测）
- ✅ 学生数据验证和修正（与Security_Middleware数据库对比）

### 移动端H5页面
- ✅ 年份班级选择界面（支持当前年份2025）
- ✅ 学生信息查询和选择
- ✅ 手机直接拍照（白墙背景要求）
- ✅ 照片实时预览和确认
- ✅ 微信JSAPI支付集成（20元制卡费）
- ✅ 支付状态实时跟踪
- ✅ 二维码扫描查询学生信息
- ✅ 重新上传照片功能（照片不符合要求时可重新提交）

### 微信集成功能
- ✅ 微信公众号授权登录（普通商户模式）
- ✅ 微信支付V3普通商户模式（公钥模式）
- ✅ 微信内置摄像头调用
- ✅ 微信浏览器兼容性优化

## 🏗️ 技术架构

### 后端技术栈
- **Python 3.11.9** - 主要开发语言（嵌入式版本）
- **Flask** - Web框架和API服务器
- **PostgreSQL** - 数据库（内网生产环境）
- **SQLAlchemy** - ORM框架
- **Flask-JWT-Extended** - 认证和授权
- **qrcode** - 二维码生成
- **Pillow** - 图片处理和照片优化
- **OpenCV** - 人脸检测和图像处理
- **Waitress** - WSGI生产服务器（Windows推荐）
- **requests** - HTTP请求处理

### 前端技术栈
- **Vue.js 3** - 后台管理界面
- **HTML5/CSS3** - 移动端H5页面（主题色#226e7e）
- **JavaScript ES6+** - 前端交互逻辑
- **微信JSAPI** - 支付和摄像头调用
- **Bootstrap** - 响应式布局

### 微信支付集成
- **微信支付V3 API** - 统一下单接口
- **普通商户模式** - 公钥模式
- **RSA-SHA256签名算法** - V3版本签名验证
- **JSAPI支付** - 微信内H5支付

### 数据库设计
- **persons表** - 学生信息（复用现有）
- **class_info表** - 班级信息（复用现有）
- **student_cards表** - 学生卡信息和状态
- **card_photos表** - 照片管理和存储
- **payment_records表** - 支付记录和订单状态

## 📁 项目结构

```
xian_zhong_Student_Card/
├── app/                    # 主应用目录
│   ├── __init__.py        # Flask应用初始化
│   ├── models/            # 数据模型
│   │   ├── student.py     # 学生信息模型
│   │   ├── student_card.py # 学生卡模型
│   │   ├── payment.py     # 支付记录模型
│   │   └── photo.py       # 照片管理模型
│   ├── routes/            # 路由控制器
│   │   ├── admin.py       # 后台管理路由
│   │   ├── mobile.py      # 移动端路由
│   │   ├── api.py         # API接口路由
│   │   └── wechat_pay.py  # 微信支付路由
│   ├── services/          # 业务逻辑服务
│   │   ├── wechat_pay_service_v3.py # 微信支付V3服务（公钥模式）
│   │   ├── wechat_auth_service.py   # 微信授权服务
│   │   ├── photo_service.py         # 照片处理服务
│   │   ├── qr_service.py           # 二维码生成服务
│   │   ├── card_service.py         # 学生卡业务服务
│   │   └── face_detection_service.py # 人脸检测服务
│   └── utils/             # 工具函数
│       ├── auth.py        # 认证工具
│       └── helpers.py     # 辅助函数
├── static/                # 静态资源
│   ├── css/              # 样式文件
│   ├── js/               # JavaScript文件
│   └── images/           # 图片资源（logo等）
├── templates/             # 模板文件
│   ├── admin/            # 后台管理模板
│   │   ├── dashboard.html # 管理面板
│   │   ├── students.html  # 学生管理
│   │   ├── cards.html     # 学生卡管理
│   │   └── reupload_photos.html # 重新上传照片管理
│   ├── mobile/           # 移动端模板
│   │   ├── select_class.html # 班级选择
│   │   ├── select_student.html # 学生选择
│   │   ├── upload_photo.html # 照片上传
│   │   ├── reupload_photo.html # 重新上传照片
│   │   ├── payment.html   # 支付页面
│   │   └── query.html     # 信息查询
│   └── auth/             # 认证相关模板
├── uploads/               # 上传文件目录
│   ├── photos/           # 学生照片存储
│   ├── reupload_photos/  # 重新上传的照片存储
│   ├── qrcodes/          # 二维码文件
│   └── temp/             # 临时文件
├── exports/               # 导出文件目录
│   ├── students_data.xlsx # 学生数据文件
│   └── *.xlsx            # 各种报告和导出文件
├── migrations/            # 数据库迁移脚本
├── certs/                 # 微信支付证书
├── docs/                  # 项目文档
├── config.py             # 配置文件
├── requirements.txt      # Python依赖包
├── start.py             # 开发环境启动
├── start_production.py  # 生产环境启动
└── README.md            # 项目说明文档
```

## 🚀 快速开始

### 环境要求
- **Python 3.8+** (推荐3.12)
- **MySQL 5.7+** (生产环境数据库)
- **Nginx** (生产环境反向代理)
- **微信公众号** (支付和授权)

### 本地开发环境

#### 1. 克隆项目
```bash
git clone [项目地址]
cd xian_zhong_Student_Card
```

#### 2. 安装Python依赖
```bash
# 方法1：使用安装脚本（推荐）
python install.py

# 方法2：手动安装
pip install -r requirements.txt
```

#### 3. 配置数据库
```bash
# 初始化数据库表结构
python init_db.py

# 添加新功能字段（v1.1.0+）
python migrations/add_materials_tracking.py
```

#### 4. 配置环境变量
创建 `.env` 文件（开发环境）：
```env
# 数据库配置
DATABASE_URL=mysql+pymysql://username:password@localhost:3306/database_name

# 微信支付V3配置（普通商户模式）
WECHAT_APPID=your_app_id
WECHAT_MCHID=your_merchant_id
WECHAT_API_KEY_V3=your_v3_api_key_32_characters
WECHAT_CERT_SERIAL_NO=your_cert_serial_no
WECHAT_PUBLIC_KEY_ID=your_public_key_id
WECHAT_APP_SECRET=your_app_secret

# 应用配置
SECRET_KEY=your_secret_key
FLASK_ENV=development
```

创建 `.env.production` 文件（生产环境）：
```env
# 数据库配置（PostgreSQL + 用户隔离）
DATABASE_URL=postgresql://student_card_user:StudentCard_User_2025!@127.0.0.1:5432/student_card_system

# 微信支付V3配置（普通商户模式）
WECHAT_APPID=your_app_id
WECHAT_MCHID=your_merchant_id
WECHAT_API_KEY_V3=your_v3_api_key_32_characters
WECHAT_CERT_SERIAL_NO=your_cert_serial_no
WECHAT_PUBLIC_KEY_ID=your_public_key_id
WECHAT_APP_SECRET=your_app_secret

# 应用配置
SECRET_KEY=your_production_secret_key
FLASK_ENV=production
SERVER_NAME=bg.cs588.cn
PORT=5000
```

#### 5. 启动开发服务器
```bash
python start.py
```

### 生产环境部署

#### 内网服务器环境（当前部署）
- **操作系统**: Windows Server 2012 R2
- **Python环境**: 嵌入式Python 3.11.9 (python_embedded)
- **数据库**: PostgreSQL 13+ (用户隔离)
- **Web服务器**: Waitress WSGI服务器
- **网络**: 内网隔离环境，通过Nginx反向代理

#### 1. 服务器配置
```bash
# 安装Desktop Experience功能（解决OpenCV DLL依赖）
Install-WindowsFeature Desktop-Experience

# 启动生产服务器
python_embedded\python.exe start_production.py

# 或使用批处理脚本
start.bat

# Windows服务配置（可选）
# 可以配置为Windows服务自动启动
```

#### 2. 数据库配置（PostgreSQL）
```sql
-- 创建数据库和用户
CREATE DATABASE student_card_system;
CREATE USER student_card_user WITH PASSWORD 'StudentCard_User_2025!';
GRANT ALL PRIVILEGES ON DATABASE student_card_system TO student_card_user;

-- 运行迁移脚本
python_embedded\python.exe migrations\migrate_to_postgresql.py
```

#### 3. 部署注意事项
- **OpenCV依赖**: 需要安装Desktop Experience功能解决DLL依赖问题
- **网络隔离**: 依赖包需要手动下载.whl文件本地安装
- **权限配置**: 数据库用户隔离，文件权限适当设置
- **服务监控**: 建议配置进程监控和自动重启
- **防火墙配置**: 注意Windows防火墙规则，避免过度限制导致微信支付API无法访问
- **FRP穿透**: 内网环境需要配置FRP客户端连接到阿里云服务器实现外网访问

#### 4. Nginx配置
```nginx
server {
    listen 80;
    server_name bg.cs588.cn;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl;
    server_name bg.cs588.cn;

    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;

    location / {
        proxy_pass http://127.0.0.1:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

### 访问地址
- 📱 **移动端**: https://bg.cs588.cn/mobile/
- 🖥️ **后台管理**: https://bg.cs588.cn/admin/
- 📡 **API接口**: https://bg.cs588.cn/api/
- 🔍 **二维码查询**: https://bg.cs588.cn/mobile/query/[student_id]

## 📊 开发进度

### 核心功能 ✅ 已完成
- [x] 项目初始化和结构搭建
- [x] 数据库扩展设计和模型创建
- [x] 班级格式转换功能（支持所有年级）
- [x] 二维码生成系统（包含查询URL和时效）
- [x] 后台管理界面开发（Vue.js）
- [x] 移动端H5页面开发（响应式设计）
- [x] 照片上传和处理（直接拍照，白墙背景）
- [x] 微信支付集成（V2服务商模式）
- [x] 微信公众号授权登录
- [x] 支付状态跟踪和订单管理
- [x] 数据导出功能（制卡信息）
- [x] 二维码扫描查询功能
- [x] 系统测试和生产环境部署
- [x] 学生卡状态管理系统（完整状态流转）
- [x] 材料生成模式选择功能（增量生成支持）
- [x] 年级自动修正功能（根据入学年份智能判断）
- [x] 人脸检测和智能裁剪（自动方向检测）

### 优化功能 🔄 持续改进
- [x] 移动端用户体验优化
- [x] 支付流程优化
- [x] 错误处理和日志记录
- [x] 安全性增强
- [x] 人脸检测和智能裁剪优化
- [x] 年级自动修正和有效期计算
- [x] 材料生成流程优化
- [ ] 性能优化和缓存
- [ ] 数据备份和恢复
- [ ] 监控和告警系统

## 💰 支付配置

### 支付信息
- **制卡费用**: 20元/张
- **支付方式**: 微信JSAPI支付
- **支付模式**: 普通商户模式（公钥模式）
- **签名算法**: RSA-SHA256（V3版本）

### 支付流程
1. **选择学生** → 年份班级选择 → 学生信息确认
2. **上传照片** → 微信内直接拍照 → 照片预览确认
3. **确认信息** → 学生信息和照片最终确认
4. **微信支付** → JSAPI调起支付 → 支付状态跟踪
5. **完成制卡** → 支付成功 → 生成制卡数据

### 微信商户平台配置
- **支付授权目录**: `https://bg.cs588.cn/`
- **授权域名**: `bg.cs588.cn`
- **回调通知URL**: `https://bg.cs588.cn/api/wechat/notify`

## 🔧 故障排除

### 常见问题

#### 1. 依赖安装失败
```bash
# 升级pip到最新版本
python -m pip install --upgrade pip

# 如果某个包安装失败，尝试单独安装
pip install Flask==2.3.3
pip install Flask-SQLAlchemy==3.0.5
pip install psycopg2-binary  # PostgreSQL驱动

# 如果Pillow安装失败（图片处理）
pip install --upgrade Pillow
```

#### 2. 数据库连接失败
```bash
# 检查PostgreSQL服务状态
sudo systemctl status postgresql

# 测试数据库连接
psql -h localhost -U username -d database_name
```
- 确保PostgreSQL服务已启动
- 检查数据库连接配置（.env文件）
- 确认数据库用户权限和密码

#### 3. 微信支付问题
**V3支付配置错误**：
- 确认使用V3普通商户模式配置（公钥模式）
- 检查APPID、商户号、V3密钥配置
- 验证证书序列号是否正确（从商户证书文件获取）
- 确保公钥ID配置正确（从微信商户平台获取）
- 确保私钥文件路径正确且文件存在
- 确保同时有apiclient_key.pem（私钥）和pub_key.pem（微信支付公钥）文件

**"当前页面的URL未注册"错误**：
- 检查微信商户平台支付授权目录配置
- 确保配置为顶级域名：`https://bg.cs588.cn/`
- 等待配置生效（通常5分钟内）

**支付签名错误**：
- V3版本使用RSA-SHA256签名算法
- 检查商户私钥文件格式是否正确
- 验证证书序列号和公钥ID配置
- 确保Authorization头和Wechatpay-Serial头正确设置

**"无效的openid"错误**：
- 确认openid与当前APPID匹配
- 检查微信授权流程是否使用正确的APPID
- 验证WECHAT_APP_SECRET配置是否正确

**WinError 10013 网络连接被拒绝**：
- 症状：创建支付订单时出现"以一种访问权限不允许的方式做了一个访问套接字的尝试"
- 原因：Windows防火墙规则阻止了Python程序的网络连接
- 解决方案：
  ```powershell
  # 检查是否有阻止规则
  Get-NetFirewallRule -DisplayName "StudentCard-*" | Format-Table DisplayName, Direction, Action, Enabled

  # 删除阻止规则（如果存在）
  Remove-NetFirewallRule -DisplayName "StudentCard-BlockOther" -ErrorAction SilentlyContinue

  # 确保允许HTTPS访问
  New-NetFirewallRule -DisplayName "StudentCard-AllowHTTPS" -Direction Outbound -Program "D:\soft_work\student_card_system\python_embedded\python.exe" -RemotePort 443 -Protocol TCP -Action Allow
  ```
- 注意：部署文档中的严格防火墙配置可能会阻止微信支付API访问，需要适当调整

#### 4. 照片上传失败
```bash
# 检查上传目录权限
chmod 755 uploads/
chmod 755 uploads/photos/

# 检查磁盘空间
df -h
```

#### 5. 二维码生成失败
```bash
# 重新安装qrcode库
pip uninstall qrcode
pip install qrcode[pil]==7.4.2
```

#### 6. Nginx配置问题
```bash
# 测试Nginx配置
sudo nginx -t

# 重新加载配置
sudo systemctl reload nginx

# 查看错误日志
sudo tail -f /var/log/nginx/error.log
```

### 配置文件说明

#### 环境变量配置 (.env)
```env
# 数据库配置
DATABASE_URL=postgresql://user:pass@localhost:5432/dbname

# 微信支付V3配置（普通商户模式）
WECHAT_APPID=wx1234567890abcdef
WECHAT_MCHID=1234567890
WECHAT_API_KEY_V3=your_32_character_v3_api_key
WECHAT_CERT_SERIAL_NO=your_cert_serial_no
WECHAT_PUBLIC_KEY_ID=your_public_key_id
WECHAT_APP_SECRET=your_app_secret

# 应用配置
SECRET_KEY=your_secret_key_here
FLASK_ENV=production
CARD_PAYMENT_AMOUNT=20.00
```

#### 生产环境配置 (config.py)
```python
class ProductionConfig:
    SQLALCHEMY_DATABASE_URI = os.getenv('DATABASE_URL')
    SECRET_KEY = os.getenv('SECRET_KEY')
    WECHAT_APPID = os.getenv('WECHAT_APPID')
    WECHAT_API_KEY_V3 = os.getenv('WECHAT_API_KEY_V3')
    # ... 其他配置
```

## 🎯 核心功能详解

### 学生卡状态管理
系统支持完整的学生卡生命周期管理：

**状态定义**：
- **PENDING** (待支付): 学生刚申请，未支付
- **PAID** (已支付): 学生已支付费用，等待制作
- **COMPLETED** (已完成): 实体卡制作完成
- **EXPIRED** (已过期): 学生卡已过期
- **CANCELLED** (已取消): 申请被取消

**状态转换**：
1. PENDING → PAID: 学生完成微信支付
2. PAID → COMPLETED: 管理员手动标记制作完成
3. 任意状态 → CANCELLED: 管理员手动取消

### 材料生成模式
支持三种材料生成模式，提高工作效率：

1. **全部已支付学生卡** (`all`): 生成所有已支付状态的学生卡材料
2. **仅未生成过材料的学生卡** (`new`): 只生成从未生成过材料的学生卡
3. **仅重新生成已有材料的学生卡** (`regenerate`): 重新生成已有材料的学生卡

**使用场景**：
- 首次批量制卡：选择"仅未生成过材料"
- 补充制卡：选择"仅未生成过材料"
- 重新制卡：选择"仅重新生成已有材料"

### 年级自动修正功能
根据学生入学年份和当前年份，自动计算正确年级：

**修正逻辑**：
- **初中**: 入学年份 + 0年 = 初一，+1年 = 初二，+2年及以上 = 初三
- **高中**: 入学年份 + 0年 = 高一，+1年 = 高二，+2年及以上 = 高三

**修正信息**：
- 显示原始填写年级和修正后年级
- 提供详细的修正说明
- 标注无法自动判断的情况，需要人工核实

### 人脸检测和智能裁剪
基于MediaPipe的AI人脸检测技术：

**功能特点**：
- 自动检测人脸位置和方向
- 智能旋转图片到正确方向
- 按学生卡标准比例裁剪（约0.8:1）
- 输出标准尺寸（400x500像素）

**处理流程**：
1. 检测图片方向（0°、90°、180°、270°）
2. 选择人脸检测置信度最高的方向
3. 自动旋转到正确方向
4. 智能裁剪并保持人脸居中

### 重新上传照片功能
为解决照片不符合要求的问题，提供便捷的重新上传渠道：

**移动端功能**：
- 在首页底部提供"重新上传照片"入口
- 显示标准照片范例和拍摄要求
- 支持填写学生信息（姓名、年级班级、入学年份）
- 支持相机拍摄和文件上传
- 智能文件命名（包含学生信息和时间戳）

**管理端功能**：
- 专门的重新上传照片管理页面
- 查看所有重新上传的照片列表
- 显示学生信息和上传时间
- 支持照片预览和下载
- 统计功能（总数、今日上传数等）

### 学生数据验证系统
与Security_Middleware数据库集成，确保数据一致性：

**验证功能**：
- 检查学生卡系统中的学生在Security_Middleware中的存在性
- 生成详细的对比报告和缺失学生清单
- 支持PostgreSQL数据库连接和查询
- 自动生成Excel格式的验证报告

**数据修正**：
- 根据Security_Middleware数据自动修正学生信息
- 修正年级、班级、入学年份等关键字段
- 计算正确的学生卡有效期
- 生成修正日志和备份文件

**使用脚本**：
- `check_students_postgres.py` - 学生存在性验证
- `compare_student_data.py` - 学生数据对比和修正
- `final_correct_student_info.py` - 学生信息最终修正脚本

## 📱 使用说明

### 家长操作流程

#### 首次办理学生卡
1. **微信扫码** → 扫描学校提供的二维码进入系统
2. **选择年份班级** → 选择学生所在的入学年份和班级
3. **选择学生** → 从班级列表中选择自己的孩子
4. **拍摄照片** → 使用微信内置摄像头直接拍照（要求白墙背景）
5. **确认信息** → 预览照片和学生信息
6. **微信支付** → 支付20元制卡费用
7. **完成制卡** → 支付成功后等待学校制作实体卡

#### 重新上传照片
1. **进入系统** → 访问移动端首页
2. **点击重新上传** → 点击页面底部"重新上传照片"按钮
3. **查看照片要求** → 查看标准照片范例和拍摄要求
4. **填写信息** → 输入学生姓名、年级班级、入学年份
5. **上传新照片** → 拍摄或选择符合要求的照片
6. **提交审核** → 提交后等待学校处理新照片

### 管理员操作流程

#### 日常管理
1. **登录后台** → 访问管理员界面
2. **学生管理** → 查看和管理学生信息
3. **照片审核** → 审核家长上传的照片
4. **支付管理** → 查看支付记录和订单状态
5. **状态管理** → 更新学生卡状态（制作中、已完成等）

#### 重新上传照片管理
1. **照片管理** → 点击导航栏"重新上传照片"或首页"照片管理"
2. **查看列表** → 查看所有重新上传的照片和学生信息
3. **照片审核** → 查看照片质量，确认是否符合制卡要求
4. **处理照片** → 下载照片用于重新制卡或联系家长重新上传

#### 学生数据验证和修正
1. **运行验证脚本** → 使用 `python check_students_postgres.py`
   - 检查学生卡系统中的学生在Security_Middleware数据库中的存在性
   - 生成详细的验证报告Excel文件

2. **查看报告** → 检查生成的Excel报告文件
   - 确认哪些学生存在于Security_Middleware中
   - 识别需要补充或修正的学生信息

3. **运行修正脚本** → 使用 `python final_correct_student_info.py`
   - **前置条件**：需要在项目根目录放置 `密钥.txt` 文件（包含API访问密钥）
   - **功能**：自动修正学生的年级、班级、入学年份等信息
   - **特殊处理**：自动跳过班级为6个0或7个0的特殊学生
   - **输出**：生成修正结果Excel报告，包含修正前后对比

4. **处理缺失数据** → 核实和补充Security_Middleware中缺失的学生信息
5. **材料生成** → 选择生成模式（全部/仅未生成/重新生成）
6. **年级修正** → 查看和确认自动年级修正结果
7. **数据导出** → 导出制卡所需的数据文件（含人脸裁剪照片）
8. **卡片制作** → 使用导出数据制作实体学生卡

## � 数据管理脚本

### 学生信息验证脚本

#### check_students_postgres.py
**功能**：验证学生卡系统中的学生在Security_Middleware数据库中的存在性

**使用方法**：
```bash
python check_students_postgres.py
```

**输出文件**：
- `exports/学生存在性检查报告_YYYYMMDD_HHMMSS.xlsx`

**报告内容**：
- 存在的学生列表（包含匹配的Security_Middleware记录）
- 缺失的学生列表（需要在Security_Middleware中补充）
- 统计信息和匹配详情

#### final_correct_student_info.py
**功能**：根据Security_Middleware数据自动修正学生卡系统中的学生信息

**前置条件**：
1. 在项目根目录创建 `密钥.txt` 文件，内容格式：
   ```
   your_access_key
   your_secret_key
   ```
2. 确保Security_Middleware数据库连接正常
3. 确保学生卡系统API服务正常运行

#### 微信支付V3配置说明

系统使用微信支付V3普通商户模式（公钥模式）：

**配置步骤**：
1. 登录微信商户平台 → "账户中心" → "API安全"
2. 点击"申请公钥"，下载微信支付公钥文件到 `certs/pub_key.pem`
3. 复制公钥ID，配置到 `WECHAT_PUBLIC_KEY_ID`
4. 下载商户私钥文件到 `certs/apiclient_key.pem`

**重要说明**：
- **商户私钥**：用于签名请求（apiclient_key.pem）
- **微信支付公钥**：用于验证微信支付的应答和回调签名（pub_key.pem）
- 系统会自动在HTTP请求头中添加 `Wechatpay-Serial` 字段

**必需文件**：
- `certs/apiclient_key.pem` - 商户私钥文件
- `certs/apiclient_cert.pem` - 商户证书文件（用于获取序列号）

**配置示例**：
```env
WECHAT_APPID=wx1234567890abcdef
WECHAT_MCHID=1234567890
WECHAT_API_KEY_V3=your32characterv3apikeyhere123456
WECHAT_PUBLIC_KEY_ID=PUB_KEY_ID_0117232325422025072400181921000803
WECHAT_PRIVATE_KEY_PATH=certs/apiclient_key.pem
WECHAT_PUBLIC_KEY_PATH=certs/pub_key.pem
WECHAT_NOTIFY_URL=https://yourdomain.com/api/wechat/notify
```

**使用方法**：
```bash
python final_correct_student_info.py
```

**修正内容**：
- 入学年份：根据当前年度和年级自动计算
- 年级信息：从Security_Middleware同步最新年级
- 班级信息：从Security_Middleware同步班级名称
- 有效期：重新计算学生卡有效期

**特殊处理**：
- 自动跳过班级为 `000000` 或 `0000000` 的特殊学生
- 处理同名学生的匹配问题（优先级：姓名+年级+班级 > 姓名+年级 > 姓名）

**输出文件**：
- `exports/学生信息修正结果_YYYYMMDD_HHMMSS.xlsx`

**报告内容**：
- 修正成功的学生列表（修正前后对比）
- 跳过处理的特殊班级学生
- 匹配失败或未找到的学生
- 详细的修正日志和统计信息

#### test_wechat_pay_v3.py
**功能**：测试微信支付V3普通商户模式配置

**使用方法**：
```bash
python test_wechat_pay_v3.py
```

**测试内容**：
- 微信支付V3配置项检查
- 私钥文件存在性验证
- 签名生成功能测试
- Authorization头生成测试

**输出结果**：
- 配置项检查结果
- 签名功能测试结果
- 问题诊断和解决建议

### 使用建议

1. **首次使用**：先运行 `check_students_postgres.py` 了解数据状况
2. **数据修正**：确认验证结果后运行 `final_correct_student_info.py`
3. **支付测试**：运行 `test_wechat_pay_v3.py` 验证微信支付配置
4. **定期维护**：建议每学期开始时运行一次数据验证和修正

## �📝 重要注意事项

### 照片要求
- **拍摄环境**: 必须使用白墙作为背景
- **照片质量**: 清晰度符合制卡要求，人脸居中
- **拍摄方式**: 使用微信内置摄像头直接拍摄，不支持相册选择
- **照片尺寸**: 系统自动处理为制卡所需尺寸

### 支付说明
- **费用标准**: 20元/张学生卡
- **支付方式**: 仅支持微信支付
- **支付环境**: 必须在微信浏览器中完成支付
- **退款政策**: 支付成功后如需退款请联系学校

### 数据安全
- **信息保护**: 学生个人信息严格保密
- **照片存储**: 照片安全存储，仅用于制卡
- **访问控制**: 系统采用多层安全验证
- **数据备份**: 定期备份重要数据

### 技术支持
- **兼容性**: 支持微信7.0+版本
- **设备要求**: 支持摄像头的智能手机
- **网络要求**: 需要稳定的网络连接
- **浏览器**: 仅支持微信内置浏览器

## 🔗 相关项目

- **Security_Middleware** - 基础安全中间件项目
- **琼中附属中学人脸识别系统** - 人脸识别门禁系统
- **学生信息管理系统** - 学生基础信息管理

## 📞 技术支持

### 开发团队
- **项目负责人**: [联系方式]
- **技术支持**: [联系方式]
- **运维支持**: [联系方式]

### 问题反馈
- **技术问题**: 请提供详细的错误信息和操作步骤
- **功能建议**: 欢迎提出改进建议
- **紧急问题**: 请直接联系技术支持团队

---

## 📄 更新日志

### v1.0.0 (2025-07-15)
- ✅ 完成核心功能开发
- ✅ 微信支付集成成功
- ✅ 生产环境部署完成
- ✅ 系统测试通过

### v1.1.0 (2025-07-18)
- ✅ 学生卡状态管理系统
- ✅ 材料生成模式选择功能
- ✅ 年级自动修正功能
- ✅ 人脸检测和智能裁剪优化
- ✅ 有效期计算优化（实体卡格式）
- ✅ 导出数据增强（含年级修正信息）

### v1.2.0 (2025-07-21)
- ✅ 重新上传照片功能（移动端+管理端）
- ✅ 学生数据验证和修正系统
- ✅ Security_Middleware数据库集成验证
- ✅ 学生信息自动修正脚本
- ✅ 照片管理和审核优化
- ✅ 管理后台导航和功能完善

### v1.3.0 (2025-07-24)
- ✅ 微信支付V3普通商户模式升级（公钥模式）
- ✅ 微信授权系统改为普通商户模式
- ✅ RSA-SHA256签名算法实现
- ✅ 公钥模式配置和证书管理
- ✅ 支付安全性和稳定性提升
- ✅ 配置管理优化和环境变量修复

### v1.4.0 (2025-07-30)
- ✅ 内网服务器部署完成（Windows Server 2012 R2）
- ✅ 嵌入式Python环境配置（python_embedded）
- ✅ PostgreSQL数据库迁移和用户隔离
- ✅ OpenCV人脸检测功能在服务器环境调试
- ✅ Desktop Experience功能安装（解决DLL依赖）
- ✅ 生产环境配置文件和启动脚本优化
- ✅ Waitress生产服务器配置（Windows推荐）
- ✅ 网络隔离环境下的依赖包管理

### v1.4.1 (2025-07-31)
- ✅ FRP内网穿透配置完成（阿里云服务器 + 内网客户端）
- ✅ 解决PostgreSQL主键自增配置问题
- ✅ 修复Windows防火墙阻止微信支付API连接问题（WinError 10013）
- ✅ 优化防火墙规则配置，确保微信支付功能正常
- ✅ 完善故障排除文档，添加网络连接问题解决方案
- ✅ 系统在内网环境下通过外网域名正常访问和支付

### 后续版本规划
- 📋 性能优化和缓存机制
- 📋 数据分析和报表功能
- 📋 批量操作和导入功能
- 📋 移动端App开发

---

**© 2025 琼中附属中学学生卡管理系统 - 基于Security_Middleware项目开发**
