{% extends "admin/layout.html" %}

{% block title %}学生卡管理{% endblock %}

{% block extra_css %}
<style>
    .filters {
        background: white;
        padding: 20px;
        border-radius: 8px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .filter-row {
        display: flex;
        gap: 15px;
        align-items: center;
        flex-wrap: wrap;
    }

    .filter-group {
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .filter-group label {
        font-weight: 500;
        color: #333;
    }

    .filter-group select,
    .filter-group input {
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
    }

    table {
        width: 100%;
        border-collapse: collapse;
    }

    th,
    td {
        padding: 12px;
        text-align: left;
        border-bottom: 1px solid #eee;
    }

    th {
        background: #f8f9fa;
        font-weight: 600;
        color: #333;
    }

    tr:hover {
        background: #f8f9fa;
    }

    .status-badge {
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 12px;
        font-weight: 500;
    }

    .status-pending {
        background: #fff3cd;
        color: #856404;
    }

    .status-paid {
        background: #d1ecf1;
        color: #0c5460;
    }

    .status-processing {
        background: #d4edda;
        color: #155724;
    }

    .status-completed {
        background: #d4edda;
        color: #155724;
    }

    .status-cancelled {
        background: #f8d7da;
        color: #721c24;
    }

    .photo-preview {
        width: 40px;
        height: 50px;
        border-radius: 4px;
        object-fit: cover;
        cursor: pointer;
    }

    .openid-info {
        font-family: monospace;
        font-size: 12px;
        color: #666;
        cursor: help;
        max-width: 100px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    /* 调整表格以适应更多列 */
    table {
        font-size: 13px;
    }

    th,
    td {
        padding: 8px 4px;
        text-align: center;
    }

    th:nth-child(2),
    td:nth-child(2) {
        text-align: left;
    }

    /* 学生姓名左对齐 */
    th:nth-child(4),
    td:nth-child(4) {
        text-align: left;
    }

    /* 年级班级左对齐 */

    .pagination {
        padding: 20px;
        display: flex;
        justify-content: center;
        gap: 10px;
    }

    .page-btn {
        padding: 8px 12px;
        border: 1px solid #ddd;
        background: white;
        cursor: pointer;
        border-radius: 4px;
    }

    .page-btn.active {
        background: #667eea;
        color: white;
        border-color: #667eea;
    }

    .modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
    }

    .modal-content {
        background-color: white;
        margin: 5% auto;
        padding: 20px;
        border-radius: 8px;
        width: 90%;
        max-width: 500px;
        max-height: 80vh;
        overflow-y: auto;
    }

    .close {
        color: #aaa;
        float: right;
        font-size: 28px;
        font-weight: bold;
        cursor: pointer;
    }

    .close:hover {
        color: black;
    }
</style>
{% endblock %}

{% block content %}
<div class="page-header">
    <h1 class="page-title">学生卡管理</h1>
    <div class="breadcrumb">
        <a href="/admin/">首页</a> / 学生卡管理
    </div>
</div>

<div class="filters">
    <div class="filter-row">
        <div class="filter-group">
            <label>状态：</label>
            <select id="statusFilter">
                <option value="">全部状态</option>
                <option value="PENDING">待支付</option>
                <option value="PAID">已支付</option>
                <option value="COMPLETED">已完成</option>
                <option value="CANCELLED">已取消</option>
            </select>
        </div>
        <div class="filter-group">
            <label>年级：</label>
            <select id="gradeFilter">
                <option value="">全部年级</option>
                <option value="初一">初一</option>
                <option value="初二">初二</option>
                <option value="初三">初三</option>
                <option value="高一">高一</option>
                <option value="高二">高二</option>
                <option value="高三">高三</option>
            </select>
        </div>
        <div class="filter-group">
            <label>搜索：</label>
            <input type="text" id="searchInput" placeholder="学生姓名">
        </div>
        <button class="btn btn-primary" onclick="loadCards()">查询</button>
        <button class="btn btn-success" onclick="batchGenerateCardMaterials()">生成学生卡制作材料</button>
    </div>
</div>

<div class="card">
    <div class="card-header">
        <div class="card-title">学生卡列表</div>
        <div id="tableStats">加载中...</div>
    </div>

    <div id="loadingDiv" class="loading">
        正在加载学生卡数据...
    </div>

    <div id="errorDiv" class="error" style="display: none;"></div>

    <div id="tableContainer" style="display: none;">
        <table>
            <thead>
                <tr>
                    <th>学生ID</th>
                    <th>学生姓名</th>
                    <th>入学年份</th>
                    <th>年级班级</th>
                    <th>联系电话</th>
                    <th>照片</th>
                    <th>学生卡ID</th>
                    <th>卡号</th>
                    <th>状态</th>
                    <th>支付金额</th>
                    <th>微信用户</th>
                    <th>申请时间</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody id="cardsTableBody">
            </tbody>
        </table>

        <div class="pagination" id="paginationDiv">
        </div>
    </div>
</div>

<!-- 照片预览模态框 -->
<div id="photoModal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h3>学生照片</h3>
        <div id="photoContainer" style="text-align: center; margin-top: 20px;">
        </div>
    </div>
</div>

<!-- 二维码模态框 -->
<div id="qrModal" class="modal">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h3>学生卡二维码</h3>
        <div id="qrContainer" style="margin-top: 20px; text-align: center;">
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let currentPage = 1;
    let totalPages = 1;

    // 页面加载时获取数据
    document.addEventListener('DOMContentLoaded', function () {
        loadCards();
        setupModals();
    });

    async function loadCards(page = 1) {
        const status = document.getElementById('statusFilter').value;
        const grade = document.getElementById('gradeFilter').value;
        const search = document.getElementById('searchInput').value.trim();

        const params = new URLSearchParams({
            page: page,
            per_page: 20
        });

        if (status) params.append('status', status);
        if (grade) params.append('grade', grade);
        if (search) params.append('search', search);

        try {
            showLoading('tableContainer');

            console.log('正在调用API:', `/admin/api/cards?${params}`);
            const result = await apiCall(`/admin/api/cards?${params}`);
            console.log('API响应:', result);

            if (result && result.success) {
                displayCards(result.data);
            } else {
                console.error('API调用失败:', result);
                showError('errorDiv', result?.error || '加载失败');
            }
        } catch (error) {
            console.error('网络错误:', error);
            showError('errorDiv', '网络错误：' + error.message);
        }
    }

    function displayCards(data) {
        const { cards, pagination } = data;

        // 更新统计信息
        document.getElementById('tableStats').textContent =
            `共 ${pagination.total} 张学生卡，第 ${pagination.page}/${pagination.pages} 页`;

        // 更新表格
        const tbody = document.getElementById('cardsTableBody');
        tbody.innerHTML = '';

        cards.forEach(card => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>${card.student_id || '-'}</td>
                <td>${card.student_name || '-'}</td>
                <td>${card.student_enrollment_year || '-'}</td>
                <td>${(card.student_grade || '') + ' ' + (card.student_class || '')}</td>
                <td>${card.student_phone || '-'}</td>
                <td>
                    ${card.photo_url ?
                    `<img src="${card.photo_url}" class="photo-preview" onclick="showPhoto('${card.photo_url}', '${card.student_name}')" alt="学生照片">` :
                    '无照片'
                }
                </td>
                <td>${card.id || '-'}</td>
                <td>${card.card_number || '-'}</td>
                <td>
                    <span class="status-badge status-${card.status}">
                        ${getStatusText(card.status)}
                    </span>
                </td>
                <td>¥${card.amount || '0.00'}</td>
                <td>
                    <span class="openid-info" title="${card.wechat_openid || '未知'}">
                        ${card.wechat_openid ? (card.wechat_openid.substring(0, 8) + '...') : '未获取'}
                    </span>
                </td>
                <td>${formatDate(card.created_at)}</td>
                <td>
                    <button class="btn btn-primary" onclick="generateQR('${card.id}', '${card.student_name}')">生成二维码</button>
                    <button class="btn btn-secondary" onclick="downloadQR('${card.id}', '${card.student_name}')">下载二维码</button>
                </td>
            `;
            tbody.appendChild(row);
        });

        // 更新分页
        updatePagination(pagination);

        // 显示表格
        document.getElementById('loadingDiv').style.display = 'none';
        document.getElementById('errorDiv').style.display = 'none';
        document.getElementById('tableContainer').style.display = 'block';
    }

    function getStatusText(status) {
        const statusMap = {
            'PENDING': '待支付',
            'PAID': '已支付',
            'COMPLETED': '已完成',
            'CANCELLED': '已取消',
            // 兼容小写（向后兼容）
            'pending': '待支付',
            'paid': '已支付',
            'completed': '已完成',
            'cancelled': '已取消'
        };
        return statusMap[status] || status;
    }

    function updatePagination(pagination) {
        const paginationDiv = document.getElementById('paginationDiv');
        paginationDiv.innerHTML = '';

        currentPage = pagination.page;
        totalPages = pagination.pages;

        // 上一页
        if (pagination.has_prev) {
            const prevBtn = document.createElement('button');
            prevBtn.className = 'page-btn';
            prevBtn.textContent = '上一页';
            prevBtn.onclick = () => loadCards(pagination.page - 1);
            paginationDiv.appendChild(prevBtn);
        }

        // 页码
        for (let i = Math.max(1, pagination.page - 2); i <= Math.min(pagination.pages, pagination.page + 2); i++) {
            const pageBtn = document.createElement('button');
            pageBtn.className = `page-btn ${i === pagination.page ? 'active' : ''}`;
            pageBtn.textContent = i;
            pageBtn.onclick = () => loadCards(i);
            paginationDiv.appendChild(pageBtn);
        }

        // 下一页
        if (pagination.has_next) {
            const nextBtn = document.createElement('button');
            nextBtn.className = 'page-btn';
            nextBtn.textContent = '下一页';
            nextBtn.onclick = () => loadCards(pagination.page + 1);
            paginationDiv.appendChild(nextBtn);
        }
    }

    function showPhoto(photoUrl, studentName) {
        const photoContainer = document.getElementById('photoContainer');
        photoContainer.innerHTML = `
            <h4>${studentName} 的照片</h4>
            <img src="${photoUrl}" style="max-width: 100%; max-height: 400px; margin-top: 10px; border-radius: 8px;" alt="学生照片">
        `;
        document.getElementById('photoModal').style.display = 'block';
    }

    async function generateQR(cardId, studentName) {
        const qrContainer = document.getElementById('qrContainer');
        qrContainer.innerHTML = `
            <h4>${studentName} 的学生卡二维码</h4>
            <div id="qrcode-${cardId}" style="margin: 20px auto;">
                <div class="loading">正在生成二维码...</div>
            </div>
            <div id="qr-info-${cardId}" style="margin: 10px 0; font-size: 12px; color: #666;">
                检测二维码类型中...
            </div>
            <div style="margin-top: 15px;">
                <button class="btn btn-primary" onclick="downloadQRCode('${cardId}', '${studentName}')">下载二维码</button>
                <button class="btn btn-secondary" onclick="copyQRLink('${cardId}')">复制链接</button>
            </div>
        `;

        document.getElementById('qrModal').style.display = 'block';

        // 调用后端API生成二维码
        try {
            const result = await apiCall(`/admin/api/generate-qr/${cardId}`);

            if (result && result.success) {
                const qrElement = document.getElementById(`qrcode-${cardId}`);
                const qrInfoElement = document.getElementById(`qr-info-${cardId}`);

                qrElement.innerHTML = `<img src="${result.qr_path}" style="border: 1px solid #ddd; border-radius: 8px;" alt="二维码">`;

                // 检查二维码类型
                if (result.qr_type === 'dynamic') {
                    qrInfoElement.innerHTML = `
                        <span style="color: #28a745;">✅ 动态二维码</span> - 支持域名切换
                        <br>短链接：${result.qr_url}
                        ${result.short_code ? `<br>短代码：${result.short_code}` : ''}
                    `;
                    // 存储动态二维码URL用于复制
                    window.currentQRUrl = result.qr_url;
                } else {
                    qrInfoElement.innerHTML = `
                        <span style="color: #6c757d;">📱 静态二维码</span> - 固定链接
                        <br>链接：${result.qr_url || window.location.origin + '/mobile/card/' + cardId}
                    `;
                    // 存储静态二维码URL用于复制
                    window.currentQRUrl = result.qr_url || `${window.location.origin}/mobile/card/${cardId}`;
                }
            } else {
                document.getElementById(`qrcode-${cardId}`).innerHTML = '<div style="color: red;">二维码生成失败</div>';
                document.getElementById(`qr-info-${cardId}`).innerHTML = '<div style="color: red;">错误：' + (result?.error || '未知错误') + '</div>';
            }
        } catch (error) {
            console.error('生成二维码失败:', error);
            document.getElementById(`qrcode-${cardId}`).innerHTML = '<div style="color: red;">二维码生成失败</div>';
            document.getElementById(`qr-info-${cardId}`).innerHTML = '<div style="color: red;">网络错误或系统未部署</div>';
        }
    }

    async function downloadQR(cardId, studentName) {
        try {
            // 调用后端API生成二维码
            const result = await apiCall(`/admin/api/generate-qr/${cardId}`);

            if (result && result.success) {
                // 确定文件名和类型
                const qrType = result.qr_type === 'dynamic' ? '动态' : '静态';
                let fileName = `${studentName}_${qrType}二维码.png`;

                // 创建下载链接
                const link = document.createElement('a');
                link.href = result.qr_path;
                link.download = fileName;
                link.click();

                // 显示下载提示
                let message = `${qrType}二维码下载成功！\n文件名：${fileName}`;
                if (result.qr_type === 'dynamic') {
                    message += `\n短链接：${result.qr_url}`;
                    if (result.short_code) {
                        message += `\n短代码：${result.short_code}`;
                    }
                }
                alert(message);
            } else {
                alert('生成二维码失败：' + (result?.error || '未知错误'));
            }
        } catch (error) {
            console.error('下载二维码失败:', error);
            alert('下载二维码失败：' + error.message);
        }
    }



    function copyQRLink(cardId) {
        // 使用存储的URL，如果没有则使用默认的静态链接
        const link = window.currentQRUrl || `${window.location.origin}/mobile/card/${cardId}`;

        navigator.clipboard.writeText(link).then(() => {
            const linkType = link.includes('/qr/') ? '动态二维码' : '静态二维码';
            alert(`${linkType}链接已复制到剪贴板\n\n${link}`);
        }).catch(() => {
            // 降级方案
            const textArea = document.createElement('textarea');
            textArea.value = link;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            const linkType = link.includes('/qr/') ? '动态二维码' : '静态二维码';
            alert(`${linkType}链接已复制到剪贴板\n\n${link}`);
        });
    }



    async function batchGenerateCardMaterials() {
        // 询问是否处理人脸照片
        const processFacePhotos = confirm('是否启用人脸检测和裁剪功能？\n\n✅ 启用：自动检测人脸并裁剪为标准尺寸\n❌ 不启用：仅复制原始照片\n\n注意：启用人脸处理会增加处理时间，但可获得更好的照片效果。');

        // 询问生成模式
        let generationMode = 'all';
        const modeOptions = [
            { value: 'all', label: '全部已支付学生卡' },
            { value: 'new', label: '仅未生成过材料的学生卡' },
            { value: 'regenerate', label: '仅重新生成已有材料的学生卡' }
        ];

        const modePrompt = `请选择生成模式：\n\n1. 全部已支付学生卡\n2. 仅未生成过材料的学生卡\n3. 仅重新生成已有材料的学生卡\n\n请输入选项编号(1-3)：`;
        const modeChoice = prompt(modePrompt, '1');

        if (!modeChoice) return;

        switch (modeChoice.trim()) {
            case '1': generationMode = 'all'; break;
            case '2': generationMode = 'new'; break;
            case '3': generationMode = 'regenerate'; break;
            default: generationMode = 'all';
        }

        const modeLabel = modeOptions.find(opt => opt.value === generationMode).label;

        const confirmMessage = `确定要生成${modeLabel}的学生卡制作材料吗？\n\n包含内容：\n- 二维码文件\n- 学生原始照片\n${processFacePhotos ? '- 人脸裁剪照片\n' : ''}- 学生信息数据（含年级修正和有效期）\n\n文件将按学号/卡号命名，便于自动化制卡。`;

        if (!confirm(confirmMessage)) {
            return;
        }

        // 显示进度提示
        const button = event.target;
        const originalText = button.textContent;
        button.textContent = `正在生成材料(${modeLabel})...`;
        button.disabled = true;

        try {
            // 调用后端批量生成API
            const result = await apiCall('/admin/api/batch-generate-card-materials', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    process_face_photos: processFacePhotos,
                    generation_mode: generationMode
                })
            });

            if (result && result.success) {
                // 下载生成的zip文件
                const link = document.createElement('a');
                link.href = result.zip_path;
                link.download = result.zip_path.split('/').pop();
                link.click();

                const successMessage = `学生卡制作材料生成成功！\n\n包含内容：\n- qrcodes/ 二维码文件\n- photos/ 学生原始照片\n${result.face_processing_enabled ? '- cropped_photos/ 人脸裁剪照片\n' : ''}- data/ 学生信息JSON（含年级修正）\n- manifest.json 材料清单\n\n✨ 新功能：\n- 自动年级修正（根据入学年份）\n- 有效期计算（初中3年，高中3年）\n${result.face_processing_enabled ? '- 智能人脸检测和裁剪\n' : ''}\n文件已按学号/卡号命名，便于自动化处理。`;
                alert(successMessage);
            } else {
                alert('生成失败：' + (result?.error || '未知错误'));
            }
        } catch (error) {
            console.error('生成学生卡材料失败:', error);
            alert('生成失败：' + error.message);
        } finally {
            // 恢复按钮状态
            button.textContent = originalText;
            button.disabled = false;
        }
    }





    function setupModals() {
        const modals = document.querySelectorAll('.modal');
        const closeBtns = document.querySelectorAll('.close');

        closeBtns.forEach(btn => {
            btn.onclick = function () {
                modals.forEach(modal => modal.style.display = 'none');
            }
        });

        window.onclick = function (event) {
            modals.forEach(modal => {
                if (event.target == modal) {
                    modal.style.display = 'none';
                }
            });
        }
    }

    // 回车搜索
    document.getElementById('searchInput').addEventListener('keypress', function (e) {
        if (e.key === 'Enter') {
            loadCards();
        }
    });
</script>
{% endblock %}