#!/usr/bin/env python3
"""
管理员密码重置脚本
"""

import os
import sys
from werkzeug.security import generate_password_hash
from dotenv import load_dotenv

# 加载环境变量
load_dotenv('.env.production')

# 添加项目路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models.admin_user import AdminUser

def reset_password():
    """重置管理员密码"""
    app = create_app()
    
    with app.app_context():
        print("🔐 管理员密码重置工具")
        print("=" * 50)
        
        # 显示所有管理员
        users = AdminUser.query.all()
        if not users:
            print("❌ 没有找到管理员账户")
            return
        
        print("📋 当前管理员账户：")
        for i, user in enumerate(users, 1):
            status = "✅ 激活" if user.is_active else "❌ 禁用"
            print(f"{i}. {user.username} ({user.real_name}) - {user.email} - {status}")
        
        print()
        
        # 选择用户
        try:
            choice = input("请选择要重置密码的用户编号: ").strip()
            user_index = int(choice) - 1
            
            if user_index < 0 or user_index >= len(users):
                print("❌ 无效的用户编号")
                return
            
            selected_user = users[user_index]
            print(f"✅ 已选择用户: {selected_user.username} ({selected_user.real_name})")
            
        except ValueError:
            print("❌ 请输入有效的数字")
            return
        
        # 输入新密码
        print()
        new_password = input("请输入新密码: ").strip()
        if len(new_password) < 6:
            print("❌ 密码长度至少6位")
            return
        
        confirm_password = input("请确认新密码: ").strip()
        if new_password != confirm_password:
            print("❌ 两次输入的密码不一致")
            return
        
        # 确认操作
        print()
        confirm = input(f"确认为用户 '{selected_user.username}' 重置密码? (y/N): ").strip().lower()
        if confirm != 'y':
            print("❌ 操作已取消")
            return
        
        try:
            # 更新密码
            selected_user.set_password(new_password)
            
            # 确保用户是激活状态
            if not selected_user.is_active:
                selected_user.is_active = True
                print("✅ 同时激活了该用户账户")
            
            db.session.commit()
            
            print("✅ 密码重置成功！")
            print(f"用户名: {selected_user.username}")
            print(f"新密码: {new_password}")
            print(f"邮箱: {selected_user.email}")
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ 密码重置失败: {str(e)}")

def create_admin():
    """创建新管理员"""
    app = create_app()
    
    with app.app_context():
        print("👤 创建新管理员")
        print("=" * 50)
        
        username = input("用户名: ").strip()
        if not username:
            print("❌ 用户名不能为空")
            return
        
        # 检查用户名是否已存在
        if AdminUser.query.filter_by(username=username).first():
            print("❌ 用户名已存在")
            return
        
        email = input("邮箱: ").strip()
        if not email:
            print("❌ 邮箱不能为空")
            return
        
        # 检查邮箱是否已存在
        if AdminUser.query.filter_by(email=email).first():
            print("❌ 邮箱已存在")
            return
        
        real_name = input("真实姓名: ").strip()
        if not real_name:
            print("❌ 真实姓名不能为空")
            return
        
        password = input("密码: ").strip()
        if len(password) < 6:
            print("❌ 密码长度至少6位")
            return
        
        try:
            # 创建用户
            user = AdminUser(
                username=username,
                email=email,
                real_name=real_name,
                role='admin'
            )
            user.set_password(password)
            
            db.session.add(user)
            db.session.commit()
            
            print("✅ 管理员创建成功！")
            print(f"用户名: {username}")
            print(f"密码: {password}")
            print(f"邮箱: {email}")
            print(f"姓名: {real_name}")
            
        except Exception as e:
            db.session.rollback()
            print(f"❌ 创建管理员失败: {str(e)}")

def main():
    """主函数"""
    print("🔐 学生卡管理系统 - 管理员工具")
    print("=" * 50)
    print("1. 重置管理员密码")
    print("2. 创建新管理员")
    print("3. 退出")
    print()
    
    choice = input("请选择操作 (1-3): ").strip()
    
    if choice == '1':
        reset_password()
    elif choice == '2':
        create_admin()
    elif choice == '3':
        print("👋 再见！")
    else:
        print("❌ 无效选择")

if __name__ == "__main__":
    main()
