"""
微信支付相关路由
"""

import json
from datetime import datetime
from flask import Blueprint, request, jsonify, current_app, send_file
from app.services.wechat_pay_service_v3 import WeChatPayServiceV3
from app.services.payment_service import PaymentService
from app.models import StudentCard, PaymentRecord
from app.models.student_card import PaymentStatus
from app import db
import qrcode
import io
import base64

wechat_pay_bp = Blueprint("wechat_pay", __name__)


@wechat_pay_bp.route("/create-payment", methods=["POST"])
def create_payment():
    """创建微信支付订单"""
    try:
        data = request.get_json()
        student_card_id = data.get("student_card_id")
        payment_type = data.get("payment_type", "native")  # native或jsapi
        openid = data.get("openid")  # JSAPI支付需要（服务商AppID下的openid）

        if not student_card_id:
            return jsonify({"success": False, "error": "缺少学生卡ID"}), 400

        # 检查学生卡是否存在
        student_card = StudentCard.query.get(student_card_id)
        if not student_card:
            return jsonify({"success": False, "error": "学生卡不存在"}), 404

        # 检查是否已经支付
        existing_payment = PaymentRecord.query.filter_by(
            student_card_id=student_card_id, status=PaymentStatus.PAID
        ).first()

        if existing_payment:
            return jsonify({"success": False, "error": "该学生卡已经支付过了"}), 400

        # 获取支付金额
        amount = current_app.config["CARD_CONFIG"]["payment_amount"]
        description = f"学生卡制作费用 - {student_card.student.name if student_card.student else '未知学生'}"

        # 创建微信支付V3订单
        wechat_service = WeChatPayServiceV3()

        if payment_type == "jsapi" and openid:
            # JSAPI支付
            result = wechat_service.create_order(student_card_id, amount, description, openid)
        else:
            # Native支付
            result = wechat_service.create_order(student_card_id, amount, description)

        if result["success"]:
            # 创建支付记录
            payment_record = PaymentRecord(
                student_card_id=student_card_id,
                amount=amount,
                payment_method="wechat",
                status=PaymentStatus.PENDING,
                order_number=result["out_trade_no"],
                wechat_openid=openid,  # 保存openid
            )

            db.session.add(payment_record)
            db.session.commit()

            return jsonify(
                {
                    "success": True,
                    "payment_type": result["payment_type"],
                    "code_url": result.get("code_url"),
                    "prepay_id": result.get("prepay_id"),
                    "out_trade_no": result["out_trade_no"],
                    "amount": amount,
                    "qr_image_url": (
                        f"/api/wechat/qr-image/{result['out_trade_no']}" if result.get("code_url") else None
                    ),
                }
            )
        else:
            return jsonify({"success": False, "error": result["error"]}), 500

    except Exception as e:
        current_app.logger.error(f"创建微信支付订单失败: {str(e)}")
        return jsonify({"success": False, "error": "创建支付订单失败"}), 500


@wechat_pay_bp.route("/query-payment/<out_trade_no>", methods=["GET"])
def query_payment(out_trade_no):
    """查询支付状态"""
    try:
        wechat_service = WeChatPayServiceV3()
        result = wechat_service.query_order(out_trade_no)

        if result["success"]:
            # 更新本地支付记录
            payment_record = PaymentRecord.query.filter_by(order_number=out_trade_no).first()
            if payment_record:
                if result["trade_state"] == "SUCCESS":
                    # 只有当订单状态不是已支付时，才更新支付信息
                    if payment_record.status != PaymentStatus.PAID:
                        payment_record.status = PaymentStatus.PAID
                        payment_record.transaction_id = result.get("transaction_id")
                        payment_record.paid_at = datetime.now()  # 只在首次支付成功时设置

                        # 更新学生卡状态
                        student_card = payment_record.student_card
                        if student_card:
                            student_card.status = "paid"

                        db.session.commit()
                        current_app.logger.info(f"支付状态查询确认: {out_trade_no} - 首次确认支付成功")
                    else:
                        current_app.logger.info(f"支付状态查询: {out_trade_no} - 订单已支付，跳过更新")

                return jsonify(
                    {
                        "success": True,
                        "trade_state": result["trade_state"],
                        "trade_state_desc": result["trade_state_desc"],
                        "paid": result["trade_state"] == "SUCCESS",
                    }
                )
            else:
                return jsonify({"success": False, "error": "支付记录不存在"}), 404
        else:
            return jsonify({"success": False, "error": result["error"]}), 500

    except Exception as e:
        current_app.logger.error(f"查询微信支付状态失败: {str(e)}")
        return jsonify({"success": False, "error": "查询支付状态失败"}), 500


@wechat_pay_bp.route("/notify", methods=["POST"])
def payment_notify():
    """微信支付回调通知"""
    try:
        # 获取请求头
        headers = {
            "Wechatpay-Timestamp": request.headers.get("Wechatpay-Timestamp"),
            "Wechatpay-Nonce": request.headers.get("Wechatpay-Nonce"),
            "Wechatpay-Signature": request.headers.get("Wechatpay-Signature"),
            "Wechatpay-Serial": request.headers.get("Wechatpay-Serial")
        }

        # 获取请求体
        body = request.get_data(as_text=True)

        current_app.logger.info(f"收到微信支付V3回调: {body}")

        # V3回调处理
        wechat_service = WeChatPayServiceV3()
        result = wechat_service.handle_notify(headers, body)

        if result["success"]:
            out_trade_no = result["out_trade_no"]
            transaction_id = result["transaction_id"]
            trade_state = result["trade_state"]

            # 更新支付记录
            payment_record = PaymentRecord.query.filter_by(order_number=out_trade_no).first()
            if payment_record:
                if trade_state == "SUCCESS":
                    # 只有当订单状态不是已支付时，才更新支付信息
                    if payment_record.status != PaymentStatus.PAID:
                        payment_record.status = PaymentStatus.PAID
                        payment_record.transaction_id = transaction_id
                        payment_record.paid_at = datetime.now()  # 只在首次支付成功时设置

                        # 更新学生卡状态
                        student_card = payment_record.student_card
                        if student_card:
                            from app.models.student_card import CardStatus
                            student_card.status = CardStatus.PAID

                        db.session.commit()
                        current_app.logger.info(f"支付成功: {out_trade_no} - 首次确认")
                    else:
                        current_app.logger.info(f"支付回调重复: {out_trade_no} - 订单已支付，跳过更新")

                    return jsonify({"code": "SUCCESS", "message": "成功"})
                else:
                    current_app.logger.warning(f"支付状态异常: {trade_state}")
                    return jsonify({"code": "SUCCESS", "message": "成功"})
            else:
                current_app.logger.error(f"支付记录不存在: {out_trade_no}")
                return jsonify({"code": "FAIL", "message": "订单不存在"}), 404
        else:
            current_app.logger.error(f"处理回调失败: {result['error']}")
            return jsonify({"code": "FAIL", "message": "处理失败"}), 500

    except Exception as e:
        current_app.logger.error(f"微信支付回调处理失败: {str(e)}")
        return jsonify({"code": "FAIL", "message": "处理异常"}), 500


@wechat_pay_bp.route("/close-payment/<out_trade_no>", methods=["POST"])
def close_payment(out_trade_no):
    """关闭支付订单"""
    try:
        wechat_service = WeChatPayServiceV3()
        result = wechat_service.close_order(out_trade_no)

        if result["success"]:
            # 更新本地支付记录状态
            payment_record = PaymentRecord.query.filter_by(order_number=out_trade_no).first()
            if payment_record and payment_record.status == PaymentStatus.PENDING:
                payment_record.status = PaymentStatus.CANCELLED
                db.session.commit()

            return jsonify({"success": True, "message": "订单关闭成功"})
        else:
            return jsonify({"success": False, "error": result["error"]}), 500

    except Exception as e:
        current_app.logger.error(f"关闭微信支付订单失败: {str(e)}")
        return jsonify({"success": False, "error": "关闭订单失败"}), 500


@wechat_pay_bp.route("/qr-image/<out_trade_no>")
def get_qr_image(out_trade_no):
    """生成支付二维码图片"""
    try:
        # 从session或缓存中获取code_url
        # 这里简化处理，直接重新创建订单获取code_url
        payment_record = PaymentRecord.query.filter_by(order_number=out_trade_no).first()
        if not payment_record:
            return jsonify({"error": "订单不存在"}), 404

        # 重新获取支付链接
        wechat_service = WeChatPayServiceV3()
        result = wechat_service.create_order(
            payment_record.student_card_id,
            payment_record.amount,
            f"学生卡制作费用 - {payment_record.student_card.student.name if payment_record.student_card and payment_record.student_card.student else '未知学生'}",
        )

        if not result.get("success") or not result.get("code_url"):
            return jsonify({"error": "二维码生成失败"}), 404

        # 生成二维码
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        qr.add_data(result["code_url"])
        qr.make(fit=True)

        # 创建二维码图片
        img = qr.make_image(fill_color="black", back_color="white")

        # 将图片保存到内存
        img_io = io.BytesIO()
        img.save(img_io, "PNG")
        img_io.seek(0)

        return send_file(img_io, mimetype="image/png")

    except Exception as e:
        current_app.logger.error(f"生成二维码图片失败: {str(e)}")
        return jsonify({"error": "生成二维码失败"}), 500


@wechat_pay_bp.route("/jsapi-config", methods=["POST"])
def get_jsapi_config():
    """获取JSAPI支付配置"""
    try:
        data = request.get_json()
        prepay_id = data.get("prepay_id")

        if not prepay_id:
            return jsonify({"success": False, "error": "缺少prepay_id"}), 400

        # 获取JSAPI支付配置
        wechat_service = WeChatPayServiceV3()
        result = wechat_service.get_jsapi_config(prepay_id)

        if result["success"]:
            return jsonify(result)
        else:
            return jsonify({"success": False, "error": result["error"]}), 500

    except Exception as e:
        current_app.logger.error(f"获取JSAPI配置失败: {str(e)}")
        return jsonify({"success": False, "error": str(e)}), 500





@wechat_pay_bp.route("/refund-notify", methods=["POST"])
def refund_notify():
    """微信支付退款通知回调"""
    try:
        # 获取原始XML数据
        xml_data = request.get_data(as_text=True)
        current_app.logger.info(f"收到微信退款通知: {xml_data}")

        # 处理退款通知
        wechat_pay_service = WeChatPayServiceV2()
        result = wechat_pay_service.handle_refund_notify(xml_data)

        if result["success"]:
            current_app.logger.info(f"退款通知处理成功: {result}")
            response_xml = """<xml>
<return_code><![CDATA[SUCCESS]]></return_code>
<return_msg><![CDATA[OK]]></return_msg>
</xml>"""
        else:
            current_app.logger.error(f"退款通知处理失败: {result}")
            response_xml = f"""<xml>
<return_code><![CDATA[FAIL]]></return_code>
<return_msg><![CDATA[{result.get('error', '处理失败')}]]></return_msg>
</xml>"""

        return response_xml

    except Exception as e:
        current_app.logger.error(f"处理微信退款通知失败: {str(e)}")
        return f"""<xml>
<return_code><![CDATA[FAIL]]></return_code>
<return_msg><![CDATA[处理失败]]></return_msg>
</xml>"""
